name: 🐛 Bug Report
description: Report a bug in NocoDB
title: "🐛 Bug: "
labels: [Type : Bug]
body:
- type: markdown
  attributes:
    value: |
      Thank you for taking the time to fill out this bug report! ❤️

- type: checkboxes
  attributes:
    label: Please confirm that the bug report does ***not*** already exist
    description: We kindly ask you [to search the open issues](https://github.com/nocodb/nocodb/issues?q=is%3Aissue+sort%3Acreated-desc+) and ensure the bug has not already been reported before.
    options:
    - label: I confirm there is no existing issue for this bug.
      required: true

- type: textarea
  attributes:
    label: Steps to reproduce
    description: A clear and concise example on how to reproduce the issue. Please make sure to provide all relevant technical details.
  validations:
    required: true

- type: textarea
  attributes:
    label: Desired Behavior
    description: Describe the solution you'd like. A clear and concise description of what you want to happen.
  validations:
    required: true

- type: textarea
  attributes:
    label: Project Details
    description: Click on <kbd>…</kbd> button next to a base in the NocoDB sidebar on the left and select <kbd>Copy Base Info</kbd> (see the [docs](https://docs.nocodb.com/FAQs#how-to-check-my-project-info-) for details) and paste the result below.
    placeholder: |
      Or manually fill in following info:

      ```
      NocoDB used as docker : true / false
      NocoDB version : 
      Database used in NC_DB URL  : mysql | pg | sqlite3 / (defaults to sqlite3 if empty)
      Project was created  by clicking : New Project | New Project by connecting to external database
      Database on which spreadsheet is created : mysql | pg | sqlite3 / (defaults to sqlite3 if empty)
      OS on which NocoDB is running : 
      Node.js version if running as node : 
      Database version : 
      ```
  validations:
    required: true

- type: textarea
  attributes:
    label: Attachments
    description: Add relevant attachments here.
    placeholder: |
      Drag & drop relevant images or videos here.
  validations:
    required: false
