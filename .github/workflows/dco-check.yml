# Path inclusion/ exclusions:
#   https://docs.github.com/en/actions/learn-github-actions/workflow-syntax-for-github-actions#excluding-paths

name: Check DCO
on:
  pull_request:
    paths:
      - "packages/nocodb-sdk/**"
      - "packages/nc-gui/**"
      - "packages/nc-lib-gui/**"
      - "packages/nocodb/**"

jobs:
  commits_check_job:
    runs-on: ubuntu-22.04
    name: Commits Check
    steps:
      - name: Get PR Commits
        id: "get-pr-commits"
        uses: tim-actions/get-pr-commits@master
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
      - name: <PERSON><PERSON> <PERSON>
        uses: tim-actions/dco@master
        with:
          commits: ${{ steps.get-pr-commits.outputs.commits }}
