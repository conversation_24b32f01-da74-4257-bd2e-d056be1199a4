name: Dependency Review

on:
  pull_request:
    branches:
      - develop
    paths:
      - 'package.json'
      - 'pnpm-lock.yaml'
      - 'package-lock.json'
      - '**/package.json'
      - '**/pnpm-lock.yaml'
      - '**/package-lock.json'

permissions:
  contents: read

jobs:
  dependency-review:
    runs-on: [self-hosted, v3, aws]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Dependency Review
        uses: actions/dependency-review-action@v4