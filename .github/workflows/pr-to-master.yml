name: 'PR to master branch from develop'

on:
  # Triggered manually
  workflow_dispatch:
    inputs:
      tag:
        description: "Tag"
        required: true
      targetEnv:
        description: "Target Environment"
        required: true
        type: choice
        options:
        - DEV
        - PROD
  # Triggered by release-nocodb.yml
  workflow_call:
    inputs:
      tag:
        description: "Tag"
        required: true
        type: string
      targetEnv:
        description: "Target Environment"
        required: true
        type: string
        
jobs:
  pr-to-master:
    runs-on: ubuntu-22.04
    steps:
      - run: |
          echo 'triggering pr-to-master'
      - name: Checkout
        uses: actions/checkout@v3
        with:
          ref: develop

      - name: Create Pull Request
        if: ${{ github.event.inputs.targetEnv == 'PROD' || inputs.targetEnv == 'PROD' }}
        id: cpr
        uses: repo-sync/pull-request@v2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          source_branch: "develop"
          destination_branch: "master"  
          pr_title: '${{ github.event.inputs.tag || inputs.tag }} Pre-release'
          pr_label: 'Bot: Automerge'
      - name: Check outputs
        if: ${{ github.event.inputs.targetEnv == 'PROD' || inputs.targetEnv == 'PROD' }}
        run: |
          echo "Pull Request Number - ${{ steps.cpr.outputs.pr_number }}"
          echo "Pull Request URL - ${{ steps.cpr.outputs.pr_url }}"
      - name: automerge
        if: ${{ github.event.inputs.targetEnv == 'PROD' || inputs.targetEnv == 'PROD' }}
        uses: "pascalgn/automerge-action@v0.15.5"
        env:
           GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"
           PULL_REQUEST: "${{ steps.cpr.outputs.pr_number }}"
           MERGE_LABELS: "Bot: Automerge"
