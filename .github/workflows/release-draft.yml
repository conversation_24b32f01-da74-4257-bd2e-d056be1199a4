name: "Release : Draft Notes"

on:
  # Triggered manually
  workflow_dispatch:
    inputs:
      tag:
        description: "Tag"
        required: true
      prev_tag:
        description: "Previous Tag"
        required: true
      tagHeadSHA:
        description: "Use HEAD SHA for tagging"
        required: true
        type: choice
        options:
          - "Y"
  # Triggered by release-nocodb.yml
  workflow_call:
    inputs:
      tag:
        description: "Tag"
        required: true
        type: string
      prev_tag:
        description: "Previous Tag"
        required: true
        type: string
      tagHeadSHA:
        description: "Use HEAD SHA for tagging"
        required: true
        type: string
    secrets:
      NC_GITHUB_TOKEN:
        required: true

jobs:
  build:
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
          ref: ${{ github.ref }}
      - name: Get SHA
        id: get-sha
        # If it's triggered by release-nocodb.yml, 
        # the SHA from the third commit (i.e. Auto PR from master to develop) will be taken
        # else HEAD will be taken
        run: |
          TARGET_SHA=$(git rev-list -n 3 HEAD | tail -1)
          if [[ ${{ github.event.inputs.tagHeadSHA || inputs.tagHeadSHA }} == "Y" ]]; then
            TARGET_SHA=$(git rev-list -n 1 HEAD | tail -1)
          fi
          echo "TARGET_SHA=${TARGET_SHA}" >> $GITHUB_OUTPUT
          echo "Setting TARGET_SHA: ${TARGET_SHA}"
      - name: Create tag
        uses: actions/github-script@v3
        with:
          # need workflows permission but it's not in GITHUB_TOKEN scope
          # need a custom PAT with workflows permission here
          github-token: ${{ secrets.NC_GITHUB_TOKEN }}
          script: |
            github.git.createRef({
              owner: context.repo.owner,
              repo: context.repo.repo,
              ref: "refs/tags/${{ github.event.inputs.tag || inputs.tag }}",
              sha: "${{ steps.get-sha.outputs.TARGET_SHA }}"
            })
      - uses: actions/setup-node@v3
        with:
          node-version: 22.12.0
      - run: "npx github-release-notes@0.17.2  release --token ${{ secrets.GITHUB_TOKEN }}  --draft --tags ${{ github.event.inputs.tag || inputs.tag }}..${{ github.event.inputs.prev_tag || inputs.prev_tag }}"
