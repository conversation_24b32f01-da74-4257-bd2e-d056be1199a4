name: 'NocoDB Release'

on:
  # Triggered manually
  workflow_dispatch:
    inputs:
      tag:
        description: "Target Tag"
        required: false
      prev_tag:
        description: "Previous Tag"
        required: false
jobs:
  # Validate Branch
  validate-branch:
    runs-on: ubuntu-22.04
    steps:
      - run: |
          if [[ ${{ github.ref }} != 'refs/heads/master' ]]; then
            echo "NocoDB Release is only allowed to run on master branch"
            exit 1
          fi

  # Process Input
  process-input:
    runs-on: ubuntu-22.04
    needs: validate-branch
    outputs:
      target_tag: ${{ steps.process-input.outputs.target_tag }}
      prev_tag: ${{ steps.process-input.outputs.prev_tag }}
    steps:
      - id: process-input
        name: process-input
        run: |
          TARGET_TAG=${{github.event.inputs.tag}}
          PREV_TAG=${{github.event.inputs.prev_tag}}

          if [[ ${PREV_TAG} == '' ]]; then
            # fetch the latest version
            PREV_TAG=$(basename $(curl -fs -o/dev/null -w %{redirect_url} https://github.com/nocodb/nocodb/releases/latest))
          fi

          if [[ ${TARGET_TAG} == '' ]]; then
            # bump the version from PREV_TAG
            TARGET_TAG=$(echo ${PREV_TAG} | awk -F. -v OFS=. '{$NF += 1 ; print}')
          fi

          echo "TARGET_TAG=${TARGET_TAG}" >> $GITHUB_OUTPUT
          echo "PREV_TAG=${PREV_TAG}" >> $GITHUB_OUTPUT
      - name: Verify
        run : |
          echo TARGET_TAG: ${{ steps.process-input.outputs.target_tag }}
          echo PREV_TAG: ${{ steps.process-input.outputs.prev_tag }}

  # Merge develop to master
  pr-to-master:
    needs: process-input
    uses: ./.github/workflows/pr-to-master.yml
    with:
      tag: ${{ needs.process-input.outputs.target_tag }}
      targetEnv: ${{ github.event.inputs.targetEnv || 'PROD' }}

  # Build, install, publish frontend and backend to npm
  release-npm:
    needs: [pr-to-master, process-input]
    uses: ./.github/workflows/release-npm.yml
    with:
      tag: ${{ needs.process-input.outputs.target_tag }}
      targetEnv: ${{ github.event.inputs.targetEnv || 'PROD' }}
    secrets:
      NPM_TOKEN: "${{ secrets.NPM_TOKEN }}"

  # Create a PR to sync changes back to develop branch from master
  sync-to-develop:
    needs: [release-npm, process-input]
    uses: ./.github/workflows/sync-to-develop.yml

  # Draft Release Note
  release-draft-note:
    needs: [release-npm, process-input]
    uses: ./.github/workflows/release-draft.yml
    with:
      tag: ${{ needs.process-input.outputs.target_tag }}
      prev_tag: ${{ needs.process-input.outputs.prev_tag }}
      tagHeadSHA: 'N'
    secrets:
      NC_GITHUB_TOKEN: "${{ secrets.NC_GITHUB_TOKEN }}"

  # Build docker image and push to docker hub
  release-docker:
    needs: [release-draft-note, process-input]
    uses: ./.github/workflows/release-docker.yml
    with:
      currentVersion: 'N/A'
      tag: ${{ needs.process-input.outputs.target_tag }}
      targetEnv: ${{ github.event.inputs.targetEnv || 'PROD' }}
    secrets:
      DOCKERHUB_USERNAME: "${{ secrets.DOCKERHUB_USERNAME }}"
      DOCKERHUB_TOKEN: "${{ secrets.DOCKERHUB_TOKEN }}"

  # Build executables and publish to GitHub
  release-executables:
    needs: [release-draft-note, process-input]
    uses: ./.github/workflows/release-executables.yml
    with:
      tag: ${{ needs.process-input.outputs.target_tag }}
    secrets:
      NC_GITHUB_TOKEN: "${{ secrets.NC_GITHUB_TOKEN }}"

  # Close all issues with target tags 'Status: Ready for Next Release'
  # close-issues:
  #   needs: [release-docker, process-input]
  #   uses: ./.github/workflows/release-close-issue.yml
  #   with:
  #     issue_label: '🚀 Status: Ready for Next Release'
  #     version: ${{ needs.process-input.outputs.target_tag }}

  aiopusher:
    uses: ./.github/workflows/nix-aiopusher.yml
