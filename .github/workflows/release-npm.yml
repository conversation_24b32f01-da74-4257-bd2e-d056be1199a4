name: "Release : NPM packages"

on:
  # Triggered manually
  workflow_dispatch:
    inputs:
      tag:
        description: "Tag"
        required: true
      targetEnv:
        description: "Target Environment"
        required: true
        type: choice
        options:
          - DEV
          - PROD
  # Triggered by release-nocodb.yml / release-nightly-dev.yml / release-pr.yml
  workflow_call:
    inputs:
      tag:
        description: "Tag"
        required: true
        type: string
      targetEnv:
        description: "Target Environment"
        required: true
        type: string
    secrets:
      NPM_TOKEN:
        required: true
#       GITHUB_TOKEN:
#         required: true

jobs:
  release:
    runs-on: ubuntu-22.04
    env:
      working-directory: ./packages/nocodb
    steps:
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          ref: ${{ github.ref }}
      - name: pnpm Setup and Publish with 18.19.1
        # Setup .npmrc file to publish to npm
        uses: actions/setup-node@v3
        with:
          node-version: 22.12.0
          registry-url: "https://registry.npmjs.org"
      - run: |
          export NODE_OPTIONS="--max_old_space_size=16384"
          NOCODB_SDK_PKG_NAME=nocodb-sdk
          # If targetEnv is DEV, then use nocodb-sdk-daily package
          if [[ ${{ github.event.inputs.targetEnv || inputs.targetEnv }} == 'DEV' ]]; then
            NOCODB_SDK_PKG_NAME=nocodb-sdk-daily
          fi
          echo $NOCODB_SDK_PKG_NAME
          targetEnv=${{ github.event.inputs.targetEnv || inputs.targetEnv }} targetVersion=${{ github.event.inputs.tag || inputs.tag }} node scripts/bumpNocodbSdkVersion.js &&
          pnpm --filter=${NOCODB_SDK_PKG_NAME} install --ignore-scripts --no-frozen-lockfile --ignore-workspace && pnpm --filter=${NOCODB_SDK_PKG_NAME} run build && pnpm --filter=${NOCODB_SDK_PKG_NAME} publish --no-git-checks &&
          echo "$(date)" &&
          sleep 300 &&
          echo "$(date)" &&
          targetEnv=${{ github.event.inputs.targetEnv || inputs.targetEnv }} node scripts/upgradeNocodbSdk.js &&
          targetEnv=${{ github.event.inputs.targetEnv || inputs.targetEnv }} targetVersion=${{ github.event.inputs.tag || inputs.tag }} node scripts/bumpNcGuiVersion.js &&
          pnpm --filter=nc-gui install --ignore-scripts --no-frozen-lockfile &&
          targetEnv=${{ github.event.inputs.targetEnv || inputs.targetEnv }} targetVersion=${{ github.event.inputs.tag || inputs.tag }} pnpm --filter=nc-gui run build:copy:publish --no-git-checks &&
          echo "$(date)" &&
          sleep 300 &&
          echo "$(date)" &&
          # Build nocodb integrations
          pnpm run integrations:build && pnpm run registerIntegrations &&
          targetEnv=${{ github.event.inputs.targetEnv || inputs.targetEnv }} node scripts/upgradeNcGui.js && cd packages/nocodb && pnpm run obfuscate:build:publish --no-git-checks
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
      - name: Create Pull Request
        if: ${{ github.event.inputs.targetEnv == 'PROD' || inputs.targetEnv == 'PROD' }}
        id: cpr
        uses: peter-evans/create-pull-request@v4.2.3
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          signoff: true
          branch: "release/${{ github.event.inputs.tag || inputs.tag }}"
          delete-branch: true
          title: "Release ${{ github.event.inputs.tag || inputs.tag }}"
          labels: "Bot: Automerge"
      - name: Check outputs
        if: ${{ github.event.inputs.targetEnv == 'PROD' || inputs.targetEnv == 'PROD' }}
        run: |
          echo "Pull Request Number - ${{ steps.cpr.outputs.pull-request-number }}"
          echo "Pull Request URL - ${{ steps.cpr.outputs.pull-request-url }}"
      - name: automerge
        if: ${{ github.event.inputs.targetEnv == 'PROD' || inputs.targetEnv == 'PROD' }}
        uses: "pascalgn/automerge-action@v0.15.5"
        env:
          GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"
          PULL_REQUEST: "${{ steps.cpr.outputs.pull-request-number }}"
          MERGE_LABELS: "Bot: Automerge"
