name: "Release: Previous Docker"


on:
  workflow_dispatch:
    inputs:
      tag:
        description: "Previous Docker image tag"
        required: true

jobs:
  login:
    runs-on: ubuntu-22.04
    steps:
      -
        name: Login to Docker Hub
        uses: docker/login-action@v2.1.0
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      -
        name: Docker retag and push
        run: |
          docker pull nocodb/nocodb:${{ github.event.inputs.tag }}
          docker image tag nocodb/nocodb:${{ github.event.inputs.tag }} nocodb/nocodb:latest
          docker push nocodb/nocodb:latest
