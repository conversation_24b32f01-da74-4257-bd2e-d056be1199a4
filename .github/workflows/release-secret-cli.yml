name: "Release : Secret CLI NPM & Executables"

on:
  # Triggered manually
  workflow_dispatch:
    inputs:
      tag:
        description: "Tag name"
        required: true
    secrets:
      # Replace with `NC_GITHUB_TOKEN` once replaced with a token which have access to `nocodb/nc-secret-mgr`
      NC_GITHUB_TOKEN_TEMP:
        required: true
jobs:
  build-and-publish:
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v3
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9
      - name: Setup Node 22.12.0
        # Setup .npmrc file to publish to npm
        uses: actions/setup-node@v3
        with:
          node-version: 22.12.0
          registry-url: "https://registry.npmjs.org"

      - name: Cache pkg modules
        id: cache-pkg
        uses: actions/cache@v3
        env:
          cache-name: cache-pkg
        with:
          # pkg cache files are stored in `~/.pkg-cache`
          path: ~/.pkg-cache
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-build-${{ env.cache-name }}-
            ${{ runner.os }}-build-
            ${{ runner.os }}-

      - name: Npm package build and publish
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
        run: |
          pnpm bootstrap
          cd ./packages/nocodb
          pnpm run build:cli:module
          cd ../nc-secret-mgr
          targetVersion=${{ github.event.inputs.tag || inputs.tag }} node ../../scripts/updateCliVersion.js
          pnpm run build && pnpm run npm:publish

      # for building images for all platforms these libraries are required in Linux
      - name: Install QEMU and ldid
        run: |
          sudo apt update
          # Install qemu
          sudo apt install qemu binfmt-support qemu-user-static -y
          # install ldid
          git clone https://github.com/daeken/ldid.git
          cd ./ldid
          ./make.sh
          sudo cp ./ldid /usr/local/bin

      - uses: actions/setup-node@v3
        with:
          node-version: 22.12.0

      - name: Install nocodb, other dependencies and build executables
        run: |
          cd ./packages/nc-secret-mgr

          # install npm dependendencies
          pnpm i

          # Build sqlite binaries for all platforms
          ./node_modules/.bin/node-pre-gyp install --directory=./node_modules/sqlite3 --target_platform=win32 --fallback-to-build --target_arch=x64 --target_libc=unknown
          ./node_modules/.bin/node-pre-gyp install --directory=./node_modules/sqlite3 --target_platform=win32 --fallback-to-build --target_arch=ia32 --target_libc=unknown
          ./node_modules/.bin/node-pre-gyp install --directory=./node_modules/sqlite3 --target_platform=darwin --fallback-to-build --target_arch=x64 --target_libc=unknown
          ./node_modules/.bin/node-pre-gyp install --directory=./node_modules/sqlite3 --target_platform=darwin --fallback-to-build --target_arch=arm64 --target_libc=unknown
          ./node_modules/.bin/node-pre-gyp install --directory=./node_modules/sqlite3 --target_platform=linux --fallback-to-build --target_arch=x64 --target_libc=glibc
          ./node_modules/.bin/node-pre-gyp install --directory=./node_modules/sqlite3 --target_platform=linux --fallback-to-build --target_arch=arm64 --target_libc=glibc
          ./node_modules/.bin/node-pre-gyp install --directory=./node_modules/sqlite3 --target_platform=linux --fallback-to-build --target_arch=x64 --target_libc=musl
          ./node_modules/.bin/node-pre-gyp install --directory=./node_modules/sqlite3 --target_platform=linux --fallback-to-build --target_arch=arm64 --target_libc=musl

          # clean up code to optimize size
          npx modclean --patterns="default:*" --run

          # build executables
          npm run build:pkg

          ls ./dist-pkg

          # Move macOS executables for signing
          mkdir ./mac-dist
          mv ./dist-pkg/nc-secret-mgr-macos-arm64 ./mac-dist/
          mv ./dist-pkg/nc-secret-mgr-macos-x64 ./mac-dist/

      - name: Upload executables(except mac executables) to release
        uses: svenstaro/upload-release-action@v2
        with:
          repo_token: ${{ secrets.NC_GITHUB_TOKEN_TEMP }}
          file: packages/nc-secret-mgr/dist-pkg/**
          tag: ${{ github.event.inputs.tag || inputs.tag }}
          overwrite: true
          file_glob: true
          repo_name: nocodb/nc-secret-mgr

      - uses: actions/upload-artifact@master
        with:
          name: ${{ github.event.inputs.tag || inputs.tag }}
          path: packages/nc-secret-mgr/mac-dist
          retention-days: 1
  sign-mac-executables:
    runs-on: macos-latest
    needs: build-and-publish
    steps:
      - uses: actions/download-artifact@master
        with:
          name: ${{ github.event.inputs.tag || inputs.tag }}
          path: packages/nc-secret-mgr/mac-dist

      - name: Sign macOS executables
        run: |
          /usr/bin/codesign --force -s - ./packages/nc-secret-mgr/mac-dist/nc-secret-mgr-macos-arm64 -v
          /usr/bin/codesign --force -s - ./packages/nc-secret-mgr/mac-dist/nc-secret-mgr-macos-x64 -v

      - uses: actions/upload-artifact@master
        with:
          name: ${{ format('{0}-signed', github.event.inputs.tag || inputs.tag) }}
          path: packages/nc-secret-mgr/mac-dist
          retention-days: 1

  publish-mac-executables:
    needs: [sign-mac-executables, build-and-publish]
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/download-artifact@master
        with:
          name: ${{ format('{0}-signed', github.event.inputs.tag || inputs.tag) }}
          path: packages/nc-secret-mgr/mac-dist

      - name: Upload executables(except mac executables) to release
        uses: svenstaro/upload-release-action@v2
        with:
          repo_token: ${{ secrets.NC_GITHUB_TOKEN_TEMP }}
          file: packages/nc-secret-mgr/mac-dist/**
          tag: ${{ github.event.inputs.tag || inputs.tag }}
          overwrite: true
          file_glob: true
          repo_name: nocodb/nc-secret-mgr
