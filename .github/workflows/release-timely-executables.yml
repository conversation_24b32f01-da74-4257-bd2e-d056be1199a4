name: "Release : Timely Executables"

on:
  # Triggered manually
  workflow_dispatch:
    inputs:
      tag:
        description: "Timely version"
        required: true
  # Triggered by release-nightly-dev.yml / release-pr.yml
  workflow_call:
    inputs:
      tag:
        description: "Timely version"
        required: true
        type: string
    secrets:
      NC_GITHUB_TOKEN:
        required: true
jobs:
  build-executables:
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v3
        with:
          token: ${{ secrets.NC_GITHUB_TOKEN }}
          repository: 'nocodb/nocodb-timely'
      - name: Cache node modules
        id: cache-npm
        uses: actions/cache@v3
        env:
          cache-name: cache-node-modules
        with:
          # npm cache files are stored in `~/.npm` on Linux/macOS
          path: ~/.npm
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-build-${{ env.cache-name }}-
            ${{ runner.os }}-build-
            ${{ runner.os }}-
      - name: Cache pkg modules
        id: cache-pkg
        uses: actions/cache@v3
        env:
          cache-name: cache-pkg
        with:
          # pkg cache files are stored in `~/.pkg-cache`
          path: ~/.pkg-cache
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-build-${{ env.cache-name }}-
            ${{ runner.os }}-build-
            ${{ runner.os }}-
      - name: Install QEMU and ldid
        run: |
          sudo apt update
          # Install qemu
          sudo apt install qemu binfmt-support qemu-user-static
          # install ldid
          git clone https://github.com/daeken/ldid.git
          cd ./ldid
          ./make.sh
          sudo cp ./ldid /usr/local/bin

      - uses: actions/setup-node@v3
        with:
          node-version: 22.12.0

      - name: Update nocodb-timely
        env:
          TAG: ${{ github.event.inputs.tag || inputs.tag }}
        run: |
          npm i -E nocodb-daily@$TAG

          git config user.name 'github-actions[bot]'
          git config user.email 'github-actions[bot]@users.noreply.github.com'

          git commit package.json -m "Update to $TAG"
          git tag $TAG
          git push --tags

      - name : Install dependencies and build executables
        run: |
          # install npm dependencies
          npm i

          # Copy sqlite binaries
          # rsync -rvzhP ./binaries/binding/ ./node_modules/sqlite3/lib/binding/

          # Build sqlite binaries for all platforms
          ./node_modules/.bin/node-pre-gyp install --directory=./node_modules/sqlite3 --target_platform=win32 --fallback-to-build --target_arch=x64 --target_libc=unknown
          ./node_modules/.bin/node-pre-gyp install --directory=./node_modules/sqlite3 --target_platform=win32 --fallback-to-build --target_arch=ia32 --target_libc=unknown
          ./node_modules/.bin/node-pre-gyp install --directory=./node_modules/sqlite3 --target_platform=darwin --fallback-to-build --target_arch=x64 --target_libc=unknown
          ./node_modules/.bin/node-pre-gyp install --directory=./node_modules/sqlite3 --target_platform=darwin --fallback-to-build --target_arch=arm64 --target_libc=unknown
          ./node_modules/.bin/node-pre-gyp install --directory=./node_modules/sqlite3 --target_platform=linux --fallback-to-build --target_arch=x64 --target_libc=glibc
          ./node_modules/.bin/node-pre-gyp install --directory=./node_modules/sqlite3 --target_platform=linux --fallback-to-build --target_arch=arm64 --target_libc=glibc
          ./node_modules/.bin/node-pre-gyp install --directory=./node_modules/sqlite3 --target_platform=linux --fallback-to-build --target_arch=x64 --target_libc=musl
          ./node_modules/.bin/node-pre-gyp install --directory=./node_modules/sqlite3 --target_platform=linux --fallback-to-build --target_arch=arm64 --target_libc=musl
          
          # clean up code to optimize size
          npx modclean --patterns="default:*" --ignore="nc-lib-gui-daily/**,nocodb-daily/**,dayjs/**,express-status-monitor/**,sqlite3/**" --run
          
          mkdir -p ./mac-dist
          mkdir -p ./dist
          
          # build darwin x64 executable
          npm uninstall sharp
          npm install --cpu=x64 --os=darwin sharp
          npx --yes pkg@5.8.1 . --compress GZip -t node22-macos-x64 -o Noco-macos-x64
          mv ./Noco-macos-x64 ./mac-dist/
          
          # build darwin arm64 executable
          npm uninstall sharp
          npm install --cpu=arm64 --os=darwin sharp
          npx --yes pkg@5.8.1 . --compress GZip -t node22-macos-arm64 -o Noco-macos-arm64
          mv ./Noco-macos-arm64 ./mac-dist/
          
          
          # build linux x64 executable
          npm uninstall sharp
          npm install --cpu=x64 --os=linux sharp
          npx --yes pkg@5.8.1 . --compress GZip -t node22-linux-x64 -o Noco-linux-x64
          mv ./Noco-linux-x64 ./dist/

          # build linux arm64 executable
          npm uninstall sharp
          npm install --cpu=arm64 --os=linux sharp
          npx --yes pkg@5.8.1 . --compress GZip -t node22-linux-arm64 -o Noco-linux-arm64
          mv ./Noco-linux-arm64 ./dist/


          # build windows x64 executable
          npm uninstall sharp
          npm install --cpu=x64 --os=win32 sharp
          npx --yes pkg@5.8.1 . --compress GZip -t node22-win-x64 -o Noco-win-x64.exe
          mv ./Noco-win-x64.exe ./dist/

          # build windows arm64 executable
          npm uninstall sharp
          npm install --cpu=arm64 --os=win32 sharp
          npx --yes pkg@5.8.1 . --compress GZip -t node22-win-arm64 -o Noco-win-arm64.exe
          mv ./Noco-win-arm64.exe ./dist/

      - name: Upload executables(except mac executables) to release
        uses: svenstaro/upload-release-action@v2
        with:
          repo_token: ${{ secrets.NC_GITHUB_TOKEN }}
          file: dist/**
          tag: ${{ github.event.inputs.tag || inputs.tag }}
          overwrite: true
          file_glob: true
          repo_name: nocodb/nocodb-timely

      - uses: actions/upload-artifact@master
        with:
          name: ${{ github.event.inputs.tag || inputs.tag }}
          path: mac-dist
          retention-days: 1

  sign-mac-executables:
    runs-on: macos-latest
    needs: build-executables
    steps:

      - uses: actions/download-artifact@master
        with:
          name: ${{ github.event.inputs.tag || inputs.tag }}
          path: mac-dist

      - name: Sign macOS executables
        run: |
          /usr/bin/codesign --force -s - ./mac-dist/Noco-macos-arm64 -v
          /usr/bin/codesign --force -s - ./mac-dist/Noco-macos-x64 -v

      - uses: actions/upload-artifact@master
        with:
          name: ${{ format('{0}-signed', github.event.inputs.tag || inputs.tag) }}
          path: mac-dist
          retention-days: 1


  publish-mac-executables:
    needs: sign-mac-executables
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/download-artifact@master
        with:
          name: ${{ format('{0}-signed', github.event.inputs.tag || inputs.tag) }}
          path: mac-dist

      - name: Upload mac executables to release
        uses: svenstaro/upload-release-action@v2
        with:
          repo_token: ${{ secrets.NC_GITHUB_TOKEN }}
          file: mac-dist/**
          tag: ${{ github.event.inputs.tag || inputs.tag }}
          overwrite: true
          file_glob: true
          repo_name: nocodb/nocodb-timely


