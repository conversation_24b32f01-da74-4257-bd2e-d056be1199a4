# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions

name: Backend Unit Tests

on:
  push:
    branches: ["develop"]
    paths:
      - "packages/nocodb/**"
  pull_request:
    branches: ["develop"]
    paths:
      - "packages/nocodb/**"

jobs:
  unit-tests:
    runs-on: ubuntu-22.04

    strategy:
      matrix:
        node-version: 22.12.0
        # See supported Node.js release schedule at https://nodejs.org/en/about/releases/

    steps:
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9
      - uses: actions/checkout@v3
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
          cache: "pnpm"
      - name: remove use-node-version from .npmrc
        run: sed -i '/^use-node-version/d' .npmrc
      - name: install dependencies
        run: pnpm bootstrap
      - name: run unit tests
        working-directory: ./packages/nocodb
        run: pnpm run unit-test
