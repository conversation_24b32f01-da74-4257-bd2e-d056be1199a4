name: "Update SDK Path"

on:
  # Triggered manually
  workflow_dispatch:
  # Triggered by release-nocodb.yml
  workflow_call:
jobs:
  release:
    runs-on: ubuntu-22.04
    steps:
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9
      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: 22.12.0
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - run: |
          sed -i '/^use-node-version/d' .npmrc      
          pnpm bootstrap

      - name: Create Pull Request
        id: cpr
        uses: peter-evans/create-pull-request@v4.2.3
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          signoff: true
          branch: "bot/update-nocodb-sdk-path"
          delete-branch: true
          title: "Update nocodb-sdk to local path"
          labels: "Bot: Automerge"
      - name: Check outputs
        run: |
          echo "Pull Request Number - ${{ steps.cpr.outputs.pull-request-number }}"
          echo "Pull Request URL - ${{ steps.cpr.outputs.pull-request-url }}"
      - name: automerge
        uses: "pascalgn/automerge-action@v0.15.5"
        env:
          GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"
          PULL_REQUEST: "${{ steps.cpr.outputs.pull-request-number }}"
          MERGE_LABELS: "Bot: Automerge"
