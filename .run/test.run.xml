<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="test" type="js.build_tools.npm" nameIsGenerated="true">
    <package-json value="$PROJECT_DIR$/scripts/playwright/package.json" />
    <command value="run" />
    <scripts>
      <script value="test" />
    </scripts>
    <node-interpreter value="project" />
    <envs />
    <method v="2" />
  </configuration>
</component>