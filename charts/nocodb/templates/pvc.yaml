{{ if .Values.storage.enabled }}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ include "nocodb.fullname" . }}
  labels:
    {{- include "nocodb.selectorLabels" . | nindent 8 }}
spec:
  resources:
    requests:
      storage: {{ .Values.storage.size }}
  storageClassName: {{ .Values.storage.storageClassName }}
  accessModes:
    {{- default (toYaml .Values.storage.accessModes) "- ReadWriteMany" | nindent 4 }}
  volumeMode: Filesystem
{{ end }}
