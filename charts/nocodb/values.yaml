# Default values for nocodb.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: nocodb/nocodb
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 8080

ingress:
  enabled: false
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths: []
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}

extraEnvs:
  NC_PUBLIC_URL: https://nocodb.local.org

extraSecretEnvs:
  NC_AUTH_JWT_SECRET: secretString
  NC_DB: "mysql2://mysql:3306?u=nocodb&p=secretPass&d=nocodb"

storage:
  # If disabled, another persistent storage should be configured for attachments to work.
  # We recommend setting NC_S3_BUCKET_NAME and other NC_S3* environment variables.
  # Refer documentation for more details.
  enabled: true
  size: 3Gi
  storageClassName: ""

postgresql:
  enabled: false
  auth:
    database: nocodb
    username: nocodb
    password: secretPass
  persistence:
    size: 8Gi

mysql:
  enabled: false
  auth:
    database: nocodb
    username: nocodb
    password: secretPass
  persistence:
    enabled: false
    size: 8Gi
