# NocoDB : Auto-upstall script

### Usage

```bash
./noco.sh
````

### Notes

This simple command : 🕊
- 🐳 Automatically installs all pre-requisites like docker, docker-compose
- 🚀 Automatically installs NocoDB with PostgreSQL, Redis, Minio, Traefik gateway using Docker Compose. 🐘 🗄️ 🌐
- 🔄 Automatically upgrades NocoDB to the latest version when you run the command again.
- 🔒 Automatically setups SSL and also renews it. Needs a domain or subdomain as input while installation.
