version: '2.1'
services: 
  nocodb: 
    depends_on: 
      root_db: 
        condition: service_healthy
    environment: 
      NC_DB: "pg://root_db:5432?u=postgres&p=password&d=root_db"
    image: "nocodb/nocodb:latest"
    ports: 
      - "8080:8080"
    restart: always
    volumes: 
      - "nc_data:/usr/app/data"
  root_db: 
    environment: 
      POSTGRES_DB: root_db
      POSTGRES_PASSWORD: password
      POSTGRES_USER: postgres
    healthcheck: 
      interval: 10s
      retries: 10
      test: "pg_isready -U \"$$POSTGRES_USER\" -d \"$$POSTGRES_DB\""
      timeout: 2s
    image: postgres:16.6
    restart: always
    volumes: 
      - "db_data:/var/lib/postgresql/data"
volumes: 
  db_data: {}
  nc_data: {}
