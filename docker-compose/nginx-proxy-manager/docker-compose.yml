version: '2.1'
networks: 
  default: ~
  nginxproxy: ~
services: 
  nginx-proxy-manager: 
    environment: 
      PGID: 1000
      PUID: 1000
      TZ: Europe/Amsterdam
    image: jlesage/nginx-proxy-manager
    networks: 
      - nginxproxy
    ports: 
      - "8181:8181"
      - "80:8080"
      - "443:4443"
    restart: always
    volumes: 
      - "nginx-proxy-manager:/config:rw"
  nocodb: 
    depends_on: 
      root_db: 
        condition: service_healthy
    environment: 
      NC_DB: "mysql2://root_db:3306?u=noco&p=password&d=root_db"
    image: "nocodb/nocodb:latest"
    networks: 
      - default
      - nginxproxy
    restart: always
    volumes: 
      - "nc_data:/usr/app/data"
  root_db: 
    environment: 
      MYSQL_DATABASE: root_db
      MYSQL_PASSWORD: password
      MYSQL_ROOT_PASSWORD: password
      MYSQL_USER: noco
    healthcheck: 
      retries: 10
      test: 
        - CMD
        - mysqladmin
        - ping
        - "-h"
        - localhost
      timeout: 20s
    image: "mysql:8.3.0"
    networks: 
      - default
    restart: always
    volumes: 
      - "db_data:/var/lib/mysql"
volumes: 
  db_data: {}
  nc_data: {}
  nginx-proxy-manager: {}
