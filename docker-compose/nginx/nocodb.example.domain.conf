upstream nocodb {
    server  nocodb_app:8080;
}

server {
	server_name nocodb.example.domain;
	listen 80;
	listen [::]:80;
    # Redirect to ssl
	return 301 https://$host$request_uri;
}

server {
	server_name nocodb.example.domain;
	listen 443 ssl http2;
	listen [::]:443 ssl http2;

	#SSL configuration
	include	/etc/nginx/ssl.conf;
	include	/etc/nginx/certbot-challenge.conf;

    location / {
        proxy_pass http://nocodb;
		proxy_set_header X-Forwarded-Proto $scheme;
		proxy_set_header Upgrade $http_upgrade;
		proxy_set_header Connection 'upgrade';
		proxy_set_header Host $host;
    }
}