# Configuration file for the PostgreSQL server.

# PostgreSQL's database directory
PGROOT="/var/lib/postgres"

# PostgreSQL's database data directory
PGDATA="/var/lib/postgres/data"

# PostgreSQL's log file.
PGLOG="/var/log/postgres.log"

# Passed to initdb if necessary
INITOPTS="-A peer --auth-host=md5 --auth-local=peer"

# Extra options to run postmaster with, e.g.:
# -N is the maximal number of client connections
# -B is the number of shared buffers and has to be at least 2x the value for -N
# Please read the man-page to postmaster for more options. Many of these options
# can be set directly in the configuration-file.
#PGOPTS="-N 512 -B 1024"

# This configures the directives used for s6-log in the log service.
DIRECTIVES="n3 s2000000 T"
