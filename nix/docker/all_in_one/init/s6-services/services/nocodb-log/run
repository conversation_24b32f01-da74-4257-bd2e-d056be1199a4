#!/bin/execlineb -P

envfile /etc/s6-confs/nocodb.conf

importas -sCuD "n3 s2000000 1 T" DIRECTIVES DIRECTIVES

ifelse { test -w /var/log } {
	foreground { install -d -o s6log -g s6log /var/log/nocodb }
	s6-setuidgid s6log exec -c s6-log -d3 -b -- ${DIRECTIVES} /var/log/nocodb
}

foreground { install -d -o s6log -g s6log /run/log/nocodb }

s6-setuidgid s6log exec -c s6-log -d3 -b -- ${DIRECTIVES} /run/log/nocodb
