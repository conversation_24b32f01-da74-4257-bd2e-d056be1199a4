#!/bin/execlineb -P

envfile /etc/s6-confs/postgresql.conf

importas -sCuD "n3 s2000000 1 T" DIRECTIVES DIRECTIVES

ifelse { test -w /var/log } {
	foreground { install -d -o s6log -g s6log /var/log/postgresql }
	s6-setuidgid s6log exec -c s6-log -d3 -b -- ${DIRECTIVES} /var/log/postgresql
}

foreground { install -d -o s6log -g s6log /run/log/postgresql }

s6-setuidgid s6log exec -c s6-log -d3 -b -- ${DIRECTIVES} /run/log/postgresql
