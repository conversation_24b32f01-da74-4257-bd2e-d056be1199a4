@import url('./variables.css');

html {
  font-size: 16px;
  word-spacing: 1px;
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
  font-family: Inter, Manrope, 'Source Sans Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
    sans-serif;
}

body,
* {
  font-family: Inter, Manrope, 'Source Sans Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
    sans-serif;
}

/*
Apply Inter for rtl
*/

.rtl .v-application *:not(.material-icons) {
  font-family: Inter !important;
}

.rtl .v-application .ml-n1 {
  margin-left: 0px !important;
}

/*
For Drag and Drop
*/
.grabbing * {
  cursor: grabbing;
}

.flex.hidden {
  display: none;
}
